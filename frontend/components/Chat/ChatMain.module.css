.chatMain {
  display: flex;
  flex-direction: column;
}

.noConversation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-light);
  font-size: 16px;
  gap: 10px;
}

.noConversation i {
  font-size: 48px;
  color: var(--border-gray);
  margin-bottom: 10px;
}

.noConversation h3 {
  margin: 0;
  color: var(--primary-navy);
}

.noConversation p {
  margin: 0;
  font-size: 14px;
}

.emptyMessages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-light);
  gap: 10px;
}

.emptyMessages i {
  font-size: 32px;
  color: var(--border-gray);
}

.chatHeader {
  padding: 15px;
  border-bottom: 1px solid var(--border-gray);
  display: flex;
  align-items: center;
  gap: 10px;
}

.chatHeaderName {
  font-weight: 600;
  color: var(--primary-navy);
  font-size: 14px;
}

.chatHeaderStatus {
  font-size: 11px;
  color: var(--text-light);
}

.chatHeaderActions {
  margin-left: auto;
  display: flex;
  gap: 12px;
}

.chatHeaderActions i {
  cursor: pointer;
  padding: 8px;
  color: var(--text-light);
  transition: color 0.3s;
}

.chatHeaderActions i:hover {
  color: var(--primary-navy);
}

.chatMessages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #fafbfc;
}

.message {
  display: flex;
  margin-bottom: 12px;
}

.message.own {
  justify-content: flex-end;
}

.messageBubble {
  max-width: 70%;
  padding: 10px 14px;
  border-radius: 16px;
  font-size: 13px;
  line-height: 1.3;
}

.message.own .messageBubble {
  background-color: var(--primary-navy);
  color: var(--white);
  border-bottom-right-radius: 4px;
}

.message:not(.own) .messageBubble {
  background-color: var(--white);
  color: var(--primary-navy);
  border-bottom-left-radius: 4px;
  box-shadow: var(--shadow);
}

.messageTime {
  font-size: 10px;
  color: var(--text-light);
  margin-top: 3px;
  text-align: right;
}

.message:not(.own) .messageTime {
  text-align: left;
}

.chatInput {
  padding: 15px;
  border-top: 1px solid var(--border-gray);
  background-color: var(--white);
}

.chatInputContainer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chatInputContainer input {
  flex: 1;
  padding: 10px 14px;
  border: 1px solid var(--border-gray);
  border-radius: 20px;
  font-size: 13px;
}

.chatInputContainer i {
  cursor: pointer;
  color: var(--text-light);
}

.chatSendBtn {
  background-color: var(--primary-navy);
  color: var(--white);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.chatSendBtn:hover {
  background-color: var(--secondary-navy);
}

.chatSendBtn:disabled {
  background-color: var(--border-gray);
  cursor: not-allowed;
}

.messageSender {
  font-size: 11px;
  color: var(--text-light);
  margin-bottom: 2px;
  font-weight: 500;
}

.readStatus {
  margin-left: 4px;
  font-size: 10px;
}

.readStatus.fa-check-double {
  color: var(--primary-blue);
}

/* Typing indicator styles */
.typingIndicator {
  display: flex;
  align-items: flex-end;
  margin-bottom: 12px;
  gap: 8px;
}

.typingBubble {
  background-color: var(--white);
  border-radius: 16px;
  border-bottom-left-radius: 4px;
  padding: 10px 14px;
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  min-height: 20px;
}

.typingDots {
  display: flex;
  gap: 3px;
}

.typingDots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--text-light);
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typingDots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typingDots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingAnimation {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typingText {
  font-size: 11px;
  color: var(--text-light);
  align-self: flex-end;
  margin-bottom: 2px;
}
