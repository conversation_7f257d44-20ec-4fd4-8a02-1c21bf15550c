.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background-color: var(--white);
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-heavy);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 15px;
  border-bottom: 1px solid var(--border-gray);
}

.modalHeader h2 {
  margin: 0;
  color: var(--primary-navy);
  font-size: 18px;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  font-size: 16px;
  color: var(--text-light);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.closeButton:hover {
  background-color: var(--light-gray);
}

.closeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.groupInfo {
  padding: 15px 20px;
  background-color: var(--light-gray);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-dark);
}

.groupInfo i {
  color: var(--primary-navy);
}

.eventForm {
  padding: 20px;
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--primary-navy);
  font-size: 14px;
}

.formGroup input,
.formGroup textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: var(--primary-navy);
}

.formGroup input.error,
.formGroup textarea.error {
  border-color: #e74c3c;
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.errorMessage {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.charCount {
  font-size: 11px;
  color: var(--text-light);
  text-align: right;
  margin-top: 4px;
}

.dateTimeRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.submitError {
  background-color: #fee;
  color: #e74c3c;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.modalActions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid var(--border-gray);
}

.modalActions button {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .modalOverlay {
    padding: 10px;
  }
  
  .modalContent {
    max-height: 95vh;
  }
  
  .modalHeader {
    padding: 15px 15px 10px;
  }
  
  .modalHeader h2 {
    font-size: 16px;
  }
  
  .eventForm {
    padding: 15px;
  }
  
  .dateTimeRow {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .modalActions {
    flex-direction: column-reverse;
  }
  
  .modalActions button {
    width: 100%;
  }
}