.searchContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.searchHeader {
  margin-bottom: 30px;
}

.searchForm {
  margin-bottom: 20px;
}

.searchInputContainer {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
}

.searchInputContainer i {
  position: absolute;
  left: 15px;
  color: var(--text-light);
  z-index: 1;
}

.searchInput {
  flex: 1;
  padding: 15px 20px 15px 45px;
  border: 2px solid var(--border-gray);
  border-radius: 25px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary-navy);
}

.searchButton {
  margin-left: 10px;
  padding: 15px 25px;
  background-color: var(--primary-navy);
  color: var(--white);
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.searchButton:hover {
  background-color: var(--secondary-navy);
}

.searchInfo h2 {
  margin: 0 0 5px 0;
  color: var(--primary-navy);
  font-size: 24px;
  font-weight: 600;
}

.searchInfo p {
  margin: 0;
  color: var(--text-light);
  font-size: 14px;
}

.searchTabs {
  display: flex;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  margin-bottom: 20px;
  overflow: hidden;
}

.tab {
  flex: 1;
  padding: 15px 20px;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-light);
}

.tab:hover {
  background-color: var(--light-gray);
}

.tab.active {
  background-color: var(--primary-navy);
  color: var(--white);
}

.tab:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.searchResults {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.usersResults,
.groupsResults,
.postsResults {
  padding: 20px;
}

.userCard,
.groupCard {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--border-gray);
  transition: background-color 0.2s;
}

.userCard:last-child,
.groupCard:last-child {
  border-bottom: none;
}

.userCard:hover,
.groupCard:hover {
  background-color: var(--light-gray);
}

.userAvatar,
.groupAvatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-weight: 600;
  color: var(--primary-navy);
  overflow: hidden;
}

.userAvatar img,
.groupAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.userInfo,
.groupInfo {
  flex: 1;
  margin-right: 15px;
}

.userInfo h3,
.groupInfo h3 {
  margin: 0 0 5px 0;
  color: var(--primary-navy);
  font-size: 16px;
  font-weight: 600;
}

.userInfo p,
.groupInfo p {
  margin: 0 0 3px 0;
  color: var(--text-light);
  font-size: 14px;
}

.userInfo span,
.groupInfo span {
  color: var(--text-dark);
  font-size: 13px;
  display: block;
  margin-top: 5px;
  line-height: 1.4;
}

.userActions,
.groupActions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.userActions button,
.groupActions button {
  padding: 8px 16px;
  font-size: 13px;
  white-space: nowrap;
}

.loadingState,
.errorState,
.emptyResults,
.noSearchState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: var(--text-light);
  gap: 15px;
}

.loadingState i,
.errorState i,
.emptyResults i,
.noSearchState i {
  font-size: 48px;
  color: var(--border-gray);
  margin-bottom: 10px;
}

.errorState i {
  color: #e74c3c;
}

.errorState p,
.emptyResults p,
.noSearchState p {
  margin: 0;
  color: var(--text-dark);
}

.noSearchState h2 {
  margin: 0;
  color: var(--primary-navy);
  font-size: 24px;
  font-weight: 600;
}

.errorState button {
  margin-top: 10px;
}

@media (max-width: 768px) {
  .searchContainer {
    padding: 15px;
  }
  
  .searchInputContainer {
    flex-direction: column;
    gap: 10px;
  }
  
  .searchInput {
    width: 100%;
  }
  
  .searchButton {
    margin-left: 0;
    width: 100%;
  }
  
  .searchTabs {
    flex-direction: column;
  }
  
  .tab {
    padding: 12px 15px;
  }
  
  .userCard,
  .groupCard {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 20px 15px;
  }
  
  .userAvatar,
  .groupAvatar {
    margin-right: 0;
    align-self: center;
  }
  
  .userInfo,
  .groupInfo {
    margin-right: 0;
    text-align: center;
    width: 100%;
  }
  
  .userActions,
  .groupActions {
    width: 100%;
    justify-content: center;
  }
  
  .userActions button,
  .groupActions button {
    flex: 1;
  }
}